<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { dictApi } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.System.Dict | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增字典',
    edit: '编辑字典'
  };
  return titles[props.operateType];
});

type Model = Pick<Api.System.Dict, 'name' | 'code' | 'summary' | 'options' | 'status'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    code: '',
    summary: '',
    options: [],
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'code'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  code: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  await validate();
  // request
  const { error } = props.operateType === 'edit' ? await dictApi.save(model.value) : await dictApi.add(model.value);
  loading.value = false;

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-500px">
    <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
      <NFormItem label="名称" path="name">
        <NInput v-model:value="model.name" placeholder="请输入名称" clearable />
      </NFormItem>
      <NFormItem label="编码" path="code">
        <NInput v-model:value="model.code" placeholder="请输入编码" clearable />
      </NFormItem>
      <NFormItem label="描述" path="summary">
        <NInput v-model:value="model.summary" placeholder="请输入描述" type="textarea" clearable />
      </NFormItem>
      <NFormItem label="状态" path="status">
        <NSwitch v-model:value="model.status" />
      </NFormItem>
    </NForm>
    <template #footer>
      <NSpace justify="end" :size="16">
        <NButton @click="visible = false">取消</NButton>
        <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
