/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'cms',
    path: '/cms',
    component: 'layout.base',
    meta: {
      title: 'cms',
      i18nKey: 'route.cms'
    },
    children: [
      {
        name: 'cms_meta',
        path: '/cms/meta',
        component: 'view.cms_meta',
        meta: {
          title: 'cms_meta',
          i18nKey: 'route.cms_meta'
        }
      },
      {
        name: 'cms_post',
        path: '/cms/post',
        component: 'view.cms_post',
        meta: {
          title: 'cms_post',
          i18nKey: 'route.cms_post'
        }
      }
    ]
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'system',
    path: '/system',
    component: 'layout.base',
    meta: {
      title: 'system',
      i18nKey: 'route.system'
    },
    children: [
      {
        name: 'system_api',
        path: '/system/api',
        component: 'view.system_api',
        meta: {
          title: 'system_api',
          i18nKey: 'route.system_api'
        }
      },
      {
        name: 'system_config',
        path: '/system/config',
        component: 'view.system_config',
        meta: {
          title: 'system_config',
          i18nKey: 'route.system_config'
        }
      },
      {
        name: 'system_dept',
        path: '/system/dept',
        component: 'view.system_dept',
        meta: {
          title: 'system_dept',
          i18nKey: 'route.system_dept'
        }
      },
      {
        name: 'system_dict',
        path: '/system/dict',
        component: 'view.system_dict',
        meta: {
          title: 'system_dict',
          i18nKey: 'route.system_dict'
        }
      },
      {
        name: 'system_log',
        path: '/system/log',
        component: 'view.system_log',
        meta: {
          title: 'system_log',
          i18nKey: 'route.system_log'
        }
      },
      {
        name: 'system_menu',
        path: '/system/menu',
        component: 'view.system_menu',
        meta: {
          title: 'system_menu',
          i18nKey: 'route.system_menu'
        }
      },
      {
        name: 'system_role',
        path: '/system/role',
        component: 'view.system_role',
        meta: {
          title: 'system_role',
          i18nKey: 'route.system_role'
        }
      },
      {
        name: 'system_tenant',
        path: '/system/tenant',
        component: 'view.system_tenant',
        meta: {
          title: 'system_tenant',
          i18nKey: 'route.system_tenant'
        }
      },
      {
        name: 'system_user',
        path: '/system/user',
        component: 'view.system_user',
        meta: {
          title: 'system_user',
          i18nKey: 'route.system_user'
        }
      }
    ]
  },
  {
    name: 'wms',
    path: '/wms',
    component: 'layout.base',
    meta: {
      title: 'wms',
      i18nKey: 'route.wms'
    },
    children: [
      {
        name: 'wms_area',
        path: '/wms/area',
        component: 'view.wms_area',
        meta: {
          title: 'wms_area',
          i18nKey: 'route.wms_area'
        }
      },
      {
        name: 'wms_bi',
        path: '/wms/bi',
        component: 'view.wms_bi',
        meta: {
          title: 'wms_bi',
          i18nKey: 'route.wms_bi'
        }
      },
      {
        name: 'wms_check',
        path: '/wms/check',
        component: 'view.wms_check',
        meta: {
          title: 'wms_check',
          i18nKey: 'route.wms_check'
        }
      },
      {
        name: 'wms_deliver',
        path: '/wms/deliver',
        component: 'view.wms_deliver',
        meta: {
          title: 'wms_deliver',
          i18nKey: 'route.wms_deliver'
        }
      },
      {
        name: 'wms_item',
        path: '/wms/item',
        component: 'view.wms_item',
        meta: {
          title: 'wms_item',
          i18nKey: 'route.wms_item'
        }
      },
      {
        name: 'wms_log',
        path: '/wms/log',
        component: 'view.wms_log',
        meta: {
          title: 'wms_log',
          i18nKey: 'route.wms_log'
        }
      },
      {
        name: 'wms_partner',
        path: '/wms/partner',
        component: 'view.wms_partner',
        meta: {
          title: 'wms_partner',
          i18nKey: 'route.wms_partner'
        }
      },
      {
        name: 'wms_receive',
        path: '/wms/receive',
        component: 'view.wms_receive',
        meta: {
          title: 'wms_receive',
          i18nKey: 'route.wms_receive'
        }
      },
      {
        name: 'wms_stock',
        path: '/wms/stock',
        component: 'view.wms_stock',
        meta: {
          title: 'wms_stock',
          i18nKey: 'route.wms_stock'
        }
      },
      {
        name: 'wms_transfer',
        path: '/wms/transfer',
        component: 'view.wms_transfer',
        meta: {
          title: 'wms_transfer',
          i18nKey: 'route.wms_transfer'
        }
      },
      {
        name: 'wms_user',
        path: '/wms/user',
        component: 'view.wms_user',
        meta: {
          title: 'wms_user',
          i18nKey: 'route.wms_user'
        }
      }
    ]
  }
];
