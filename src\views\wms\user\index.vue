<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { NButton, NPopconfirm, NTag } from 'naive-ui';
import type { TreeOption } from 'naive-ui';
import { deptApi, roleApi, userApi } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useDict } from '@/hooks/business/dict';
import { convertToTree } from '@/utils/common';
import SwitchStatus from '@/components/custom/switch-status.vue';
import OperateDrawer from './modules/OperateDrawer.vue';
import SearchBar from './modules/SearchBar.vue';
import SiderBar from './modules/SiderBar.vue';

const appStore = useAppStore();

const roleOptions = ref<CommonType.Option<number>[]>([]);
const deptOptions = ref<TreeOption[]>([]);
onMounted(async () => {
  // 获取角色列表
  const { data: roleList, error: roleError } = await roleApi.list({
    _page: 1,
    _limit: 1000,
    status: true
  });
  if (!roleError) {
    roleOptions.value = roleList.records.map((item: Api.System.Role) => ({
      label: item.name,
      value: item.id
    }));
  }

  // 获取部门列表
  const { data: deptList, error: deptError } = await deptApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order',
    _order: 'asc',
    status: true
  });
  if (!deptError) {
    deptOptions.value = convertToTree(deptList.records);
  }
});

const { columns, columnChecks, data, getData, getDataByPage, loading, pagination, searchParams, resetSearchParams } =
  useTable({
    apiFn: userApi.list,
    showTotal: true,
    apiParams: {
      _page: 1,
      _limit: 10,
      _sort: 'id',
      _order: 'desc',
      q: null,
      status: null,
      username: null,
      nickname: null,
      phone: null,
      email: null,
      deptId: null
    },
    columns: () => [
      {
        type: 'selection',
        align: 'center',
        width: 48
      },
      {
        key: 'id',
        title: '#',
        align: 'center',
        width: 64
      },
      {
        key: 'username',
        title: '用户名',
        align: 'left'
      },
      {
        key: 'nickname',
        title: '昵称',
        align: 'left'
      },
      {
        key: 'gender',
        title: '性别',
        align: 'left',
        render: row => {
          if (row.gender === null) return null;

          const option = useDict('number').item('Gender', row.gender);
          if (!option) return null;
          return <NTag type={option.type}>{option.label}</NTag>;
        }
      },
      {
        key: 'roleIds',
        title: '角色',
        align: 'left',
        width: 200,
        render: row => (
          <div class="flex flex-wrap items-center gap-8px">
            {row.roleIds.map(id => (
              <NTag key={id} type="primary">
                {roleOptions.value.find(item => item.value === id)?.label}
              </NTag>
            ))}
          </div>
        )
      },
      {
        key: 'phone',
        title: '手机号',
        align: 'left'
      },
      {
        key: 'email',
        title: '邮箱',
        align: 'left',
        minWidth: 200,
        ellipsis: true
      },
      {
        key: 'status',
        title: '状态',
        align: 'center',
        render: row => <SwitchStatus v-model:value={row.status} apiFn={status => userApi.save({ ...row, status })} />
      },
      {
        key: 'operate',
        title: '操作',
        align: 'center',
        fixed: 'right',
        width: 150,
        render: row => (
          <div class="flex flex-center gap-12px">
            <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
              编辑
            </NButton>
            <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
              {{
                default: () => '确定删除吗？',
                trigger: () => (
                  <NButton type="error" text size="small">
                    删除
                  </NButton>
                )
              }}
            </NPopconfirm>
          </div>
        )
      }
    ]
  });

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await userApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await userApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <SearchBar v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />

    <NLayout has-sider class="sm:flex-1-hidden card-wrapper">
      <NLayoutSider :collapsed-width="0" :width="300" show-trigger="arrow-circle" bordered>
        <SiderBar v-model:value="searchParams.deptId" :dept-options="deptOptions" @search="getDataByPage" />
      </NLayoutSider>
      <NLayoutContent class="h-full">
        <NCard :bordered="false" size="small" class="h-full">
          <template #header>
            <TableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              @add="handleAdd"
              @delete="handleBatchDelete"
              @refresh="getData"
            >
              <template #suffix>
                <TableQuickSearch v-model:value="searchParams.q" clearable @refresh="getData" />
              </template>
            </TableHeaderOperation>
          </template>
          <NDataTable
            v-model:checked-row-keys="checkedRowKeys"
            :columns="columns"
            :data="data"
            size="small"
            :flex-height="!appStore.isMobile"
            :scroll-x="962"
            :loading="loading"
            remote
            striped
            :bordered="false"
            :row-key="row => row.id"
            :pagination="pagination"
            class="b-t-1px sm:h-full b-auto"
          />
          <OperateDrawer
            v-model:visible="drawerVisible"
            :operate-type="operateType"
            :row-data="editingData"
            :role-options="roleOptions"
            :dept-options="deptOptions"
            :dept-id="searchParams.deptId"
            @submitted="getDataByPage"
          />
        </NCard>
      </NLayoutContent>
    </NLayout>
  </div>
</template>

<style scoped></style>
