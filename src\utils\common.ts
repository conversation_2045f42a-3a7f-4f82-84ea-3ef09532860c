import { $t } from '@/locales';

/**
 * Transform record to option
 *
 * @example
 *   ```ts
 *   const record = {
 *     key1: 'label1',
 *     key2: 'label2'
 *   };
 *   const options = transformRecordToOption(record);
 *   // [
 *   //   { value: 'key1', label: 'label1' },
 *   //   { value: 'key2', label: 'label2' }
 *   // ]
 *   ```;
 *
 * @param record
 */
export function transformRecordToOption<T extends Record<string, string>>(record: T) {
  return Object.entries(record).map(([value, label]) => ({
    value,
    label
  })) as CommonType.Option<keyof T>[];
}

/**
 * Translate options
 *
 * @param options
 */
export function translateOptions(options: CommonType.Option<string>[]) {
  return options.map(option => ({
    ...option,
    label: $t(option.label as App.I18n.I18nKey)
  }));
}

/**
 * Toggle html class
 *
 * @param className
 */
export function toggleHtmlClass(className: string) {
  function add() {
    document.documentElement.classList.add(className);
  }

  function remove() {
    document.documentElement.classList.remove(className);
  }

  return {
    add,
    remove
  };
}

/**
 * Convert To Tree
 *
 * @param arr
 * @param key
 */
export function convertToTree(arr: any, key: string = 'parentId') {
  const map: any[] = [];
  const tree: any[] = [];

  arr.forEach((item: any) => {
    map[item.id] = { ...item, children: [] };
  });

  arr.forEach((item: any) => {
    if (item[key]) {
      map[item[key]]?.children.push(map[item.id]);
    } else {
      tree.push(map[item.id]);
    }
  });

  map.forEach((item: any) => {
    if (item.children.length === 0) delete item.children;
  });

  return tree;
}

/**
 * Disable Tree Item
 *
 * @param tree
 * @param id
 */
export function disableTreeItem(tree: any, id: number | undefined) {
  return tree.map((item: any) => {
    if (item.id === id) {
      return { ...item, disabled: true };
    } else if (item.children) {
      return { ...item, children: disableTreeItem(item.children, id) };
    }
    return item;
  });
}

/**
 * Convert Api To Tree
 *
 * @param apis
 */
export function convertApiToTree(apis: any) {
  const root: any = [];
  apis.forEach((item: Api.System.Api) => {
    let currentLevel = root;
    item.tags.forEach((tag, index) => {
      let node = currentLevel.find((n: any) => n.id === `tag$${tag}`);
      if (!node) {
        node = { id: `tag$${tag}`, summary: tag, children: [] };
        currentLevel.push(node);
      }
      if (index === item.tags.length - 1) {
        node.children.push(item);
      }
      currentLevel = node.children;
    });
  });

  return root;
}
