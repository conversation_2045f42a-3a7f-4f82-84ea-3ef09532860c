<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { areaApi } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { disableTreeItem } from '@/utils/common';
import { useDict } from '@/hooks/business/dict';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType | 'addChild';
  /** the edit row data */
  rowData?: Api.Wms.Area | null;
  /** the tree data */
  treeData?: Api.Wms.Area[] | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType | 'addChild', string> = {
    add: '新增栏目',
    edit: '编辑栏目',
    addChild: '新增子菜单'
  };
  return titles[props.operateType];
});

type Model = Pick<Api.Wms.Area, 'name' | 'code' | 'type' | 'summary' | 'order' | 'parentId' | 'parentPath' | 'status'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    code: '',
    type: null,
    summary: '',
    order: 0,
    parentId: 0,
    parentPath: [],
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'code' | 'type'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  code: defaultRequiredRule,
  type: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }

  // 新增子栏目
  if (props.operateType === 'addChild') {
    Object.assign(model.value, { parentId: props.rowData?.id });
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  await validate();
  // request
  const { error } = props.operateType === 'edit' ? await areaApi.save(model.value) : await areaApi.add(model.value);
  loading.value = false;

  if (!error) {
    window.$message?.success(`${title.value}成功`);
    closeDrawer();
    emit('submitted');
  } else {
    window.$message?.error(`${title.value}失败`);
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});

const parentOptions = computed(() => {
  if (props.operateType === 'edit' || props.operateType === 'addChild') {
    return props.operateType === 'edit' ? disableTreeItem(props.treeData, props.rowData?.id) : props.treeData;
  }
  return props.treeData;
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="父级" path="parentId">
          <NTreeSelect
            v-model:value="model.parentId"
            :options="parentOptions"
            key-field="id"
            label-field="name"
            :disabled="operateType === 'addChild'"
            clearable
          />
        </NFormItem>
        <NFormItem label="类型" path="type">
          <NSelect v-model:value="model.type" :options="useDict().items('AreaType')" clearable />
        </NFormItem>
        <NFormItem label="名称" path="name">
          <NInput v-model:value="model.name" placeholder="请输入名称" clearable />
        </NFormItem>
        <NFormItem label="编号" path="code">
          <NInput v-model:value="model.code" placeholder="请输入编号" clearable />
        </NFormItem>
        <NFormItem label="备注" path="summary">
          <NInput v-model:value="model.summary" placeholder="请输入备注" type="textarea" clearable />
        </NFormItem>
        <NFormItem label="排序" path="order">
          <NInputNumber v-model:value="model.order" placeholder="请输入排序" :min="0" :precision="0" clearable />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSwitch v-model:value="model.status" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
