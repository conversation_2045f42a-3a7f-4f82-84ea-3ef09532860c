/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppProvider: typeof import('./../components/common/app-provider.vue')['default']
    BetterScroll: typeof import('./../components/custom/better-scroll.vue')['default']
    ButtonIcon: typeof import('./../components/custom/button-icon.vue')['default']
    CountTo: typeof import('./../components/custom/count-to.vue')['default']
    DarkModeContainer: typeof import('./../components/common/dark-mode-container.vue')['default']
    ExceptionBase: typeof import('./../components/common/exception-base.vue')['default']
    FullScreen: typeof import('./../components/common/full-screen.vue')['default']
    IconAntDesignEnterOutlined: typeof import('~icons/ant-design/enter-outlined')['default']
    IconLocalBanner: typeof import('~icons/local/banner')['default']
    IconLocalLogo: typeof import('~icons/local/logo')['default']
    IconMdiArrowDownThin: typeof import('~icons/mdi/arrow-down-thin')['default']
    IconMdiArrowUpThin: typeof import('~icons/mdi/arrow-up-thin')['default']
    IconMdiKeyboardEsc: typeof import('~icons/mdi/keyboard-esc')['default']
    IconMdiKeyboardReturn: typeof import('~icons/mdi/keyboard-return')['default']
    IconPhArrowClockwise: typeof import('~icons/ph/arrow-clockwise')['default']
    IconPhArrowCounterClockwise: typeof import('~icons/ph/arrow-counter-clockwise')['default']
    IconPhArrowRight: typeof import('~icons/ph/arrow-right')['default']
    IconPhArrowsClockwise: typeof import('~icons/ph/arrows-clockwise')['default']
    IconPhArrowsIn: typeof import('~icons/ph/arrows-in')['default']
    IconPhArrowsOut: typeof import('~icons/ph/arrows-out')['default']
    IconPhCaretRight: typeof import('~icons/ph/caret-right')['default']
    IconPhDotsSixVertical: typeof import('~icons/ph/dots-six-vertical')['default']
    IconPhDotsThree: typeof import('~icons/ph/dots-three')['default']
    IconPhMagnifyingGlass: typeof import('~icons/ph/magnifying-glass')['default']
    IconPhPlus: typeof import('~icons/ph/plus')['default']
    IconPhSlidersHorizontal: typeof import('~icons/ph/sliders-horizontal')['default']
    IconPhSquaresFour: typeof import('~icons/ph/squares-four')['default']
    IconPhTrash: typeof import('~icons/ph/trash')['default']
    IconPhUpload: typeof import('~icons/ph/upload')['default']
    LangSwitch: typeof import('./../components/common/lang-switch.vue')['default']
    LookForward: typeof import('./../components/custom/look-forward.vue')['default']
    MenuToggler: typeof import('./../components/common/menu-toggler.vue')['default']
    NBreadcrumb: typeof import('naive-ui')['NBreadcrumb']
    NBreadcrumbItem: typeof import('naive-ui')['NBreadcrumbItem']
    NButton: typeof import('naive-ui')['NButton']
    NCard: typeof import('naive-ui')['NCard']
    NCarousel: typeof import('naive-ui')['NCarousel']
    NCascader: typeof import('naive-ui')['NCascader']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCollapse: typeof import('naive-ui')['NCollapse']
    NCollapseItem: typeof import('naive-ui')['NCollapseItem']
    NColorPicker: typeof import('naive-ui')['NColorPicker']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NDynamicTags: typeof import('naive-ui')['NDynamicTags']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NFormItemGi: typeof import('naive-ui')['NFormItemGi']
    NGi: typeof import('naive-ui')['NGi']
    NGrid: typeof import('naive-ui')['NGrid']
    NInput: typeof import('naive-ui')['NInput']
    NInputGroup: typeof import('naive-ui')['NInputGroup']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NLayout: typeof import('naive-ui')['NLayout']
    NLayoutContent: typeof import('naive-ui')['NLayoutContent']
    NLayoutSider: typeof import('naive-ui')['NLayoutSider']
    NList: typeof import('naive-ui')['NList']
    NListItem: typeof import('naive-ui')['NListItem']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NMenu: typeof import('naive-ui')['NMenu']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NP: typeof import('naive-ui')['NP']
    NPopconfirm: typeof import('naive-ui')['NPopconfirm']
    NPopover: typeof import('naive-ui')['NPopover']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSlider: typeof import('naive-ui')['NSlider']
    NSpace: typeof import('naive-ui')['NSpace']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NText: typeof import('naive-ui')['NText']
    NThing: typeof import('naive-ui')['NThing']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NTree: typeof import('naive-ui')['NTree']
    NTreeSelect: typeof import('naive-ui')['NTreeSelect']
    NUpload: typeof import('naive-ui')['NUpload']
    NUploadDragger: typeof import('naive-ui')['NUploadDragger']
    NWatermark: typeof import('naive-ui')['NWatermark']
    PinToggler: typeof import('./../components/common/pin-toggler.vue')['default']
    ReloadButton: typeof import('./../components/common/reload-button.vue')['default']
    RemoteSelect: typeof import('./../components/custom/remote-select.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectBool: typeof import('./../components/custom/select-bool.vue')['default']
    SoybeanAvatar: typeof import('./../components/custom/soybean-avatar.vue')['default']
    SvgIcon: typeof import('./../components/custom/svg-icon.vue')['default']
    SwitchStatus: typeof import('./../components/custom/switch-status.vue')['default']
    SystemLogo: typeof import('./../components/common/system-logo.vue')['default']
    TableColumnSetting: typeof import('./../components/advanced/table-column-setting.vue')['default']
    TableHeaderOperation: typeof import('./../components/advanced/table-header-operation.vue')['default']
    TableQuickSearch: typeof import('./../components/advanced/table-quick-search.vue')['default']
    ThemeSchemaSwitch: typeof import('./../components/common/theme-schema-switch.vue')['default']
    UploadCover: typeof import('./../components/custom/upload-cover.vue')['default']
    UploadFiles: typeof import('./../components/custom/upload-files.vue')['default']
    WangEditor: typeof import('./../components/custom/wang-editor.vue')['default']
    WaveBg: typeof import('./../components/custom/wave-bg.vue')['default']
  }
}
