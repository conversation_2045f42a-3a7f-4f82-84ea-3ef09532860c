<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import { NButton, NImage, NPopconfirm, NTag } from 'naive-ui';
import type { DataTableSortState, TreeOption } from 'naive-ui';
import dayjs from 'dayjs';
import { metaApi, postApi } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useDict } from '@/hooks/business/dict';
import { convertToTree } from '@/utils/common';
import SwitchStatus from '@/components/custom/switch-status.vue';
import OperateDrawer from './modules/OperateDrawer.vue';
import SearchBar from './modules/SearchBar.vue';
import SiderBar from './modules/SiderBar.vue';

const appStore = useAppStore();

const metaOptions = ref<TreeOption[]>([]);
onMounted(async () => {
  const { data: metaList, error: metaError } = await metaApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'id',
    _order: 'desc',
    status: true
  });
  if (!metaError) {
    metaOptions.value = convertToTree(metaList.records);
  }
});

const flagDict = useDict('number').items('Flag');

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  pagination,
  searchParams,
  resetSearchParams,
  updateSearchParams
} = useTable({
  apiFn: postApi.list,
  showTotal: true,
  apiParams: {
    _page: 1,
    _limit: 10,
    _sort: 'order,id',
    _order: 'desc,desc',
    _expand: 'meta',
    status: null,
    title: null,
    slug: null,
    metaId: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'id',
      title: '#',
      align: 'center',
      width: 64
    },
    {
      key: 'title',
      title: '标题',
      align: 'left',
      minWidth: 300,
      ellipsis: true
    },
    {
      key: 'slug',
      title: '别名',
      align: 'left'
    },
    {
      key: 'cover',
      title: '封面',
      align: 'left',
      render: row =>
        row.cover ? <NImage class="h-28px rd-3px" lazy src={import.meta.env.VITE_IMAGE_BASE_URL + row.cover} /> : '-'
    },
    {
      key: 'flag',
      title: '标志',
      align: 'left',
      render: row => (
        <div class="flex flex-wrap items-center gap-8px">
          {row.flag.map(val => {
            const option = flagDict?.find(item => item.value === val);
            return (
              <NTag key={val} type={option?.type}>
                {option?.label}
              </NTag>
            );
          })}
        </div>
      )
    },
    {
      key: 'order',
      title: '排序',
      align: 'left',
      sorter: true
    },
    {
      key: 'createdAt',
      title: '创建时间',
      align: 'left',
      sorter: true,
      render: row => dayjs(row.createdAt).format('YYYY-MM-DD HH:mm')
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: row => <SwitchStatus v-model:value={row.status} apiFn={status => postApi.save({ ...row, status })} />
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: row => (
        <div class="flex flex-center gap-12px">
          <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确定删除吗？',
              trigger: () => (
                <NButton type="error" text size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await postApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await postApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleSort(sorter: DataTableSortState) {
  if (sorter.order) {
    updateSearchParams({
      _sort: sorter.columnKey as string,
      _order: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  } else {
    updateSearchParams({
      _sort: 'id',
      _order: 'desc'
    });
  }
  await getData();
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <SearchBar v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NLayout has-sider class="sm:flex-1-hidden card-wrapper">
      <NLayoutSider :collapsed-width="0" :width="300" show-trigger="arrow-circle" bordered>
        <SiderBar v-model:value="searchParams.metaId" :meta-options="metaOptions" @search="getDataByPage" />
      </NLayoutSider>
      <NLayoutContent class="h-full">
        <NCard :bordered="false" size="small" class="h-full">
          <template #header>
            <TableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              @add="handleAdd"
              @delete="handleBatchDelete"
              @refresh="getData"
            />
          </template>
          <NDataTable
            v-model:checked-row-keys="checkedRowKeys"
            :columns="columns"
            :data="data"
            size="small"
            :flex-height="!appStore.isMobile"
            :scroll-x="962"
            :loading="loading"
            remote
            striped
            :bordered="false"
            :row-key="row => row.id"
            :pagination="pagination"
            class="b-t-1px sm:h-full b-auto"
            @update:sorter="handleSort"
          />
          <OperateDrawer
            v-model:visible="drawerVisible"
            :operate-type="operateType"
            :row-data="editingData"
            :meta-options="metaOptions"
            :meta-id="searchParams.metaId"
            @submitted="getDataByPage"
          />
        </NCard>
      </NLayoutContent>
    </NLayout>
  </div>
</template>

<style scoped></style>
